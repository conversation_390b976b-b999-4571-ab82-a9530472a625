/**
 * Test utility to verify BYOB and regular product fetching functionality
 * This file can be used to test the updated fetchProductData function
 */

import { fetchProductData } from "@/services/fetch/product-fetch";

/**
 * Test function to verify BYOB product fetching
 */
export async function testBYOBProductFetch(productHandle: string) {
  console.log(`Testing BYOB product fetch for handle: ${productHandle}`);
  
  try {
    const result = await fetchProductData({ productHandle });
    
    if (!result.success) {
      console.error("❌ BYOB Product fetch failed:", result.error);
      return false;
    }
    
    const data = result.data;
    
    // Check if it's a BYOB product
    if ('productType' in data && data.productType === 'BYOB') {
      console.log("✅ BYOB Product detected successfully");
      console.log("📦 Main product:", data.strapiProduct?.title);
      console.log("🔗 Bundle variants count:", data.bundleVariants?.length || 0);
      
      if (data.bundleVariants && data.bundleVariants.length > 0) {
        console.log("📋 Bundle variant titles:");
        data.bundleVariants.forEach((variant, index) => {
          console.log(`  ${index + 1}. ${variant.title}`);
        });
      }
      
      return true;
    } else {
      console.log("ℹ️ Product is not BYOB type, got:", data.productType || 'unknown');
      return false;
    }
  } catch (error) {
    console.error("❌ Error testing BYOB product fetch:", error);
    return false;
  }
}

/**
 * Test function to verify regular product fetching
 */
export async function testRegularProductFetch(productHandle: string) {
  console.log(`Testing regular product fetch for handle: ${productHandle}`);
  
  try {
    const result = await fetchProductData({ productHandle });
    
    if (!result.success) {
      console.error("❌ Regular Product fetch failed:", result.error);
      return false;
    }
    
    const data = result.data;
    
    // Check if it's a regular product
    if ('productType' in data && data.productType === 'VARIANT') {
      console.log("✅ Regular Product detected successfully");
      console.log("📦 Product:", data.strapiProduct?.title);
      console.log("🏷️ Product type:", data.productType);
      
      return true;
    } else {
      console.log("ℹ️ Product is not regular VARIANT type, got:", data.productType || 'unknown');
      return false;
    }
  } catch (error) {
    console.error("❌ Error testing regular product fetch:", error);
    return false;
  }
}

/**
 * Run comprehensive tests for both product types
 */
export async function runProductFetchTests() {
  console.log("🧪 Starting Product Fetch Tests...\n");
  
  // Test with sample product handles (replace with actual handles from your system)
  const testCases = [
    {
      handle: "sample-byob-product", // Replace with actual BYOB product handle
      type: "BYOB",
      testFunction: testBYOBProductFetch,
    },
    {
      handle: "sample-regular-product", // Replace with actual regular product handle
      type: "VARIANT",
      testFunction: testRegularProductFetch,
    },
  ];
  
  const results = [];
  
  for (const testCase of testCases) {
    console.log(`\n--- Testing ${testCase.type} Product ---`);
    const success = await testCase.testFunction(testCase.handle);
    results.push({ ...testCase, success });
  }
  
  console.log("\n📊 Test Results Summary:");
  results.forEach((result) => {
    const status = result.success ? "✅ PASSED" : "❌ FAILED";
    console.log(`${status} - ${result.type} Product (${result.handle})`);
  });
  
  const allPassed = results.every((result) => result.success);
  console.log(`\n🎯 Overall: ${allPassed ? "ALL TESTS PASSED" : "SOME TESTS FAILED"}`);
  
  return allPassed;
}

/**
 * Helper function to log product data structure for debugging
 */
export function logProductDataStructure(data: any) {
  console.log("📋 Product Data Structure:");
  console.log("- Product Type:", data.productType || 'unknown');
  console.log("- Strapi Product:", !!data.strapiProduct);
  console.log("- Medusa Product:", !!data.medusaProduct);
  
  if (data.productType === 'BYOB') {
    console.log("- Bundle Variants:", data.bundleVariants?.length || 0);
    if (data.bundleVariants) {
      console.log("- Bundle Variant IDs:", data.bundleVariants.map((v: any) => v.systemId || v.id));
    }
  }
}
