import React from "react";
import { getProductDetails } from "@/libs/middlewareAPIs";
import DynamicTemplate from "@/components/DynamicTemplate";
import { PageTypeEnum } from "@/types/Page";
import { notFound } from "next/navigation";

const ProductPage = async ({
  params,
}: {
  params: Promise<{ handle: string }>;
}) => {
  const { handle } = await params;

  const productData = await getProductDetails({
    productHandle: handle,
  });

  // Handle missing product data at the page level
  if (!productData?.strapiProduct && !productData?.medusaProduct) {
    notFound();
  }

  const medusaProductData = productData?.medusaProduct;

  // Check if this is a BYOB product and handle accordingly
  if (
    productData &&
    "productType" in productData &&
    productData.productType === "BYOB"
  ) {
    // For BYOB products, we might need different rendering logic
    // For now, pass the main product data and let components handle BYOB-specific logic
    console.log("BYOB Product detected:", {
      mainProduct: productData.strapiProduct,
      bundleVariants: productData.bundleVariants,
    });
  }

  return (
    <DynamicTemplate
      page={handle}
      pageType={PageTypeEnum.Product}
      productData={productData?.strapiProduct}
      medusaProductData={medusaProductData}
    />
  );
};

export default ProductPage;
