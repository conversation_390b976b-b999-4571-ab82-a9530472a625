import React from "react";
import { getProductDetails } from "@/libs/middlewareAPIs";
import DynamicTemplate from "@/components/DynamicTemplate";
import { PageTypeEnum } from "@/types/Page";
import { notFound } from "next/navigation";

const ProductPage = async ({
  params,
}: {
  params: Promise<{ handle: string }>;
}) => {
  const { handle } = await params;

  const productData = await getProductDetails({
    productHandle: handle,
  });

  // Handle missing product data at the page level
  if (!productData?.strapiProduct && !productData?.medusaProduct) {
    notFound();
  }

  const medusaProductData = productData?.medusaProduct;

  return (
    <DynamicTemplate
      page={handle}
      pageType={PageTypeEnum.Product}
      productData={productData?.strapiProduct}
      medusaProductData={medusaProductData}
    />
  );
};

export default ProductPage;
