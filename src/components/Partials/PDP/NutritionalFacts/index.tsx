"use client";

import React from "react";
import {
  Accordion,
  AccordionItem,
  AccordionContent,
} from "@/components/ui/accordion";
import { CustomAccordionTrigger } from "@/components/Common/FAQAccordion/CustomAccordionTrigger";
import { cn } from "@/libs/utils";
import { NutritionalFactsType } from "@/types/PDP/NutritionalFacts";
/**
 * NutritionalFactsAccordion Component
 *
 * Displays nutritional facts in an expandable accordion format.
 * Uses props for theming instead of Redux.
 * Supports both VARIANT and BYOB product types.
 *
 * @param data - Nutritional facts data
 * @param borderColor - Optional border color override
 * @param iconColor - Optional icon color override
 * @param className - Additional CSS classes
 * @param productType - Product type for conditional rendering
 * @param bundleVariants - Bundle variants for BYOB products
 */
export interface NutritionalFactsAccordionProps {
  data?: NutritionalFactsType;
  borderColor?: string;
  iconColor?: string;
  className?: string;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: any[];
}

export function NutritionalFactsAccordion({
  data,
  borderColor = "#036A38", // Default theme color
  className,
  productType = "VARIANT",
  bundleVariants = [],
}: NutritionalFactsAccordionProps) {
  // Use provided colors or fall back to default theme color
  const finalBorderColor = borderColor;
  const chevronColor = "black";

  // Determine what to render based on product type
  console.log("🔍 NutritionalFactsAccordion Debug:", {
    productType,
    bundleVariantsLength: bundleVariants?.length || 0,
    bundleVariants: bundleVariants,
    hasBundleVariants:
      Array.isArray(bundleVariants) && bundleVariants.length > 0,
  });

  if (productType === "BYOB") {
    console.log("🎯 BYOB Product Type Detected - Processing Bundle Variants");

    // Debug each bundle variant's nutritional facts structure
    bundleVariants.forEach((variant, index) => {
      console.log(`📋 Bundle Variant ${index + 1}:`, {
        title: variant?.title,
        hasNutritionalFacts: !!variant?.nutritional_facts,
        nutritionalFactsStructure: variant?.nutritional_facts,
        showComponent: variant?.nutritional_facts?.show_component,
      });
    });

    // For BYOB products, render each bundle variant's nutritional facts
    const validBundleVariants = bundleVariants.filter(
      (variant) =>
        variant.nutritional_facts && variant.nutritional_facts.show_component
    );

    console.log("✅ Valid Bundle Variants Found:", {
      totalBundleVariants: bundleVariants.length,
      validBundleVariants: validBundleVariants.length,
      validVariantTitles: validBundleVariants.map((v) => v.title),
    });

    if (validBundleVariants.length === 0) {
      console.log("❌ No valid bundle variants found - returning null");
      return null;
    }

    console.log(
      "🚀 Rendering BYOB Nutritional Facts for",
      validBundleVariants.length,
      "variants"
    );

    return (
      <div className="space-y-4">
        {validBundleVariants.map((variant, variantIndex) => (
          <Accordion
            key={variantIndex}
            type="single"
            collapsible
            className={cn(`border rounded-md overflow-hidden mt-6`, className)}
            style={{ borderColor: finalBorderColor }}
          >
            {variant.nutritional_facts.nutritional_fact_details.map(
              (detail: any, detailIndex: number) => (
                <AccordionItem
                  key={detailIndex}
                  value={`nutritional-facts-${variantIndex}-${detailIndex}`}
                  className="bg-transparent border-0 border-b last:border-b-0"
                  style={{ borderBottomColor: finalBorderColor }}
                >
                  <CustomAccordionTrigger
                    className="px-6 py-4 text-left text-lg lg: lg:text-[22px] font-narrow font-semibold hover:no-underline cursor-pointer"
                    iconColor={chevronColor}
                  >
                    {variant.title} - Nutritional Facts
                  </CustomAccordionTrigger>
                  <AccordionContent className="px-4 font-normal text-base lg:text-lg font-obviously">
                    {/* Description */}
                    {variant.nutritional_facts.description && (
                      <div className="mb-4 text-gray-700">
                        {variant.nutritional_facts.description}
                      </div>
                    )}

                    {/* Nutritional Facts Table */}
                    <div className="space-y-2">
                      {detail.nutritional_fact_items.map(
                        (item: any, itemIndex: number) => (
                          <div
                            key={itemIndex}
                            className="border-b border-gray-200 pb-2"
                          >
                            <div className="flex justify-between items-center">
                              <span className="font-medium">{item.key}</span>
                              <span>{item.value}</span>
                            </div>
                            {/* Sub-items if they exist */}
                            {item.nutritional_fact_sub_items &&
                              item.nutritional_fact_sub_items.length > 0 && (
                                <div className="ml-4 mt-1 space-y-1">
                                  {item.nutritional_fact_sub_items.map(
                                    (subItem: any, subIndex: number) => (
                                      <div
                                        key={subIndex}
                                        className="flex justify-between items-center text-sm text-gray-600"
                                      >
                                        <span>{subItem.key}</span>
                                        <span>{subItem.value}</span>
                                      </div>
                                    )
                                  )}
                                </div>
                              )}
                          </div>
                        )
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              )
            )}
          </Accordion>
        ))}
      </div>
    );
  }

  // For VARIANT products, use regular data
  // Early return if no data or component should not be shown
  if (!data || !data.show_component) {
    return null;
  }

  return (
    <Accordion
      type="single"
      collapsible
      className={cn(`border rounded-md overflow-hidden mt-6`, className)}
      style={{ borderColor: finalBorderColor }}
    >
      {data.nutritional_fact_details.map((detail, detailIndex) => (
        <AccordionItem
          key={detailIndex}
          value={`nutritional-facts-${detailIndex}`}
          className="bg-transparent border-0 border-b last:border-b-0"
          style={{ borderBottomColor: finalBorderColor }}
        >
          <CustomAccordionTrigger
            className="px-6 py-4 text-left text-lg lg: lg:text-[22px] font-narrow font-semibold hover:no-underline cursor-pointer"
            iconColor={chevronColor}
          >
            Nutritional Facts
          </CustomAccordionTrigger>
          <AccordionContent className="px-4 font-normal text-base lg:text-lg font-obviously">
            {/* Description */}
            {data.description && (
              <p className="mb-4 text-lg leading-7 font-normal text-[#1a181e] font-obviously flex items-start">
                {data.description}
              </p>
            )}

            {/* Main nutritional items */}
            <ul className="list-none">
              {detail.nutritional_fact_items.map((item, itemIndex) => (
                <li key={itemIndex}>
                  {/* Main item */}
                  <div className="text-base leading-7 font-normal text-[#1a181e] font-obviously flex items-start">
                    <div className="flex-shrink-0">{item.key}</div>
                    <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
                    <div className="flex-shrink-0">{item.value}</div>
                  </div>

                  {/* Sub items */}
                  {item.nutritional_fact_sub_items &&
                    item.nutritional_fact_sub_items.length > 0 && (
                      <ul className="list-none ml-4 mt-1 space-y-1">
                        {item.nutritional_fact_sub_items.map(
                          (subItem, subIndex) => (
                            <li
                              key={subIndex}
                              className="leading-6 text-base font-normal text-[#1a181e] font-obviously flex items-start"
                            >
                              <div className="flex-shrink-0">{subItem.key}</div>
                              <div className="flex-1 border-t-2 border-dotted border-[#00000080] mx-2 mt-[18px]"></div>
                              <div className="flex-shrink-0">
                                {subItem.value}
                              </div>
                            </li>
                          )
                        )}
                      </ul>
                    )}
                </li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}

export default NutritionalFactsAccordion;
