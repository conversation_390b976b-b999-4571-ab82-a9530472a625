/**
 * NutritionalFacts Debug Component
 *
 * This component helps debug BYOB nutritional facts rendering issues
 * by providing detailed logging and test data structures.
 */

"use client";

import React from "react";
import { NutritionalFactsAccordion } from "./index";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";

// Mock bundle variants with proper nutritional facts structure
const mockBundleVariantsWithNutrition: ProductDetailsType[] = [
  {
    id: 1,
    systemId: "hazelnut-cocoa",
    title: "Hazelnut Cocoa",
    primary_color: "#8B4513",
    nutritional_facts: {
      show_component: true,
      description:
        "Rich in protein and essential nutrients for sustained energy.",
      nutritional_fact_details: [
        {
          id: 1,
          nutritional_fact_items: [
            {
              key: "Calories",
              value: "150 kcal",
              nutritional_fact_sub_items: [
                {
                  key: "From Fat",
                  value: "45 kcal",
                },
              ],
            },
            {
              key: "Total Fat",
              value: "5g",
              nutritional_fact_sub_items: [
                {
                  key: "Saturated Fat",
                  value: "1g",
                },
                {
                  key: "Trans Fat",
                  value: "0g",
                },
              ],
            },
            {
              key: "Protein",
              value: "8g",
            },
            {
              key: "Total Carbohydrates",
              value: "20g",
              nutritional_fact_sub_items: [
                {
                  key: "Dietary Fiber",
                  value: "3g",
                },
                {
                  key: "Sugars",
                  value: "12g",
                },
              ],
            },
          ],
        },
      ],
    },
  } as ProductDetailsType,
  {
    id: 2,
    systemId: "almond-millet-cocoa",
    title: "Almond Millet Cocoa",
    primary_color: "#DA70D6",
    nutritional_facts: {
      show_component: true,
      description: "Almond and millet blend with natural cocoa flavor.",
      nutritional_fact_details: [
        {
          id: 1,
          nutritional_fact_items: [
            {
              key: "Calories",
              value: "140 kcal",
              nutritional_fact_sub_items: [
                {
                  key: "From Fat",
                  value: "40 kcal",
                },
              ],
            },
            {
              key: "Total Fat",
              value: "4.5g",
              nutritional_fact_sub_items: [
                {
                  key: "Saturated Fat",
                  value: "0.5g",
                },
                {
                  key: "Trans Fat",
                  value: "0g",
                },
              ],
            },
            {
              key: "Protein",
              value: "7g",
            },
            {
              key: "Total Carbohydrates",
              value: "18g",
              nutritional_fact_sub_items: [
                {
                  key: "Dietary Fiber",
                  value: "4g",
                },
                {
                  key: "Sugars",
                  value: "10g",
                },
              ],
            },
          ],
        },
      ],
    },
  } as ProductDetailsType,
  {
    id: 3,
    systemId: "peanut-millet-cocoa",
    title: "Peanut Millet Cocoa",
    primary_color: "#DAA520",
    nutritional_facts: {
      show_component: false, // This one should be filtered out
      nutritional_fact_details: [
        {
          id: 1,
          title: "Calories",
          value: "160",
          unit: "kcal",
        },
      ],
    },
  } as ProductDetailsType,
];

// Mock bundle variants without nutritional facts
const mockBundleVariantsWithoutNutrition: ProductDetailsType[] = [
  {
    id: 1,
    systemId: "variant-1",
    title: "Variant Without Nutrition",
    primary_color: "#8B4513",
    // No nutritional_facts property
  } as ProductDetailsType,
  {
    id: 2,
    systemId: "variant-2",
    title: "Variant With Empty Nutrition",
    primary_color: "#DA70D6",
    nutritional_facts: null,
  } as ProductDetailsType,
];

/**
 * Debug Component for NutritionalFacts
 */
const NutritionalFactsDebug: React.FC = () => {
  const testScenarios = [
    {
      name: "BYOB with Valid Nutritional Facts",
      productType: "BYOB" as const,
      bundleVariants: mockBundleVariantsWithNutrition,
      description:
        "Should render 2 accordion sections (3rd variant has show_component: false)",
    },
    {
      name: "BYOB without Nutritional Facts",
      productType: "BYOB" as const,
      bundleVariants: mockBundleVariantsWithoutNutrition,
      description: "Should render nothing (no valid nutritional facts)",
    },
    {
      name: "VARIANT Product Type",
      productType: "VARIANT" as const,
      bundleVariants: mockBundleVariantsWithNutrition,
      description: "Should use regular nutritional facts rendering",
      data: {
        show_component: true,
        description: "Complete nutritional information for this product.",
        nutritional_fact_details: [
          {
            id: 1,
            nutritional_fact_items: [
              {
                key: "Calories",
                value: "200 kcal",
                nutritional_fact_sub_items: [
                  {
                    key: "From Fat",
                    value: "60 kcal",
                  },
                ],
              },
              {
                key: "Total Fat",
                value: "7g",
                nutritional_fact_sub_items: [
                  {
                    key: "Saturated Fat",
                    value: "2g",
                  },
                  {
                    key: "Trans Fat",
                    value: "0g",
                  },
                ],
              },
              {
                key: "Protein",
                value: "10g",
              },
              {
                key: "Total Carbohydrates",
                value: "25g",
                nutritional_fact_sub_items: [
                  {
                    key: "Dietary Fiber",
                    value: "5g",
                  },
                  {
                    key: "Sugars",
                    value: "15g",
                  },
                ],
              },
            ],
          },
        ],
      },
    },
    {
      name: "Empty Bundle Variants",
      productType: "BYOB" as const,
      bundleVariants: [],
      description: "Should render nothing (empty bundle variants)",
    },
  ];

  const runDebugTests = () => {
    console.log("🧪 Running NutritionalFacts Debug Tests");

    testScenarios.forEach((scenario, index) => {
      console.log(`\n📋 Test ${index + 1}: ${scenario.name}`);
      console.log("Description:", scenario.description);
      console.log("Product Type:", scenario.productType);
      console.log("Bundle Variants:", scenario.bundleVariants);

      if (scenario.productType === "BYOB") {
        const validVariants = scenario.bundleVariants.filter(
          (variant) =>
            variant.nutritional_facts &&
            variant.nutritional_facts.show_component
        );
        console.log("Valid Variants:", validVariants.length);
        console.log(
          "Valid Variant Titles:",
          validVariants.map((v) => v.title)
        );
      }
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-4">
          NutritionalFacts Debug Component
        </h1>

        {/* Debug Controls */}
        <div className="mb-6">
          <button
            onClick={runDebugTests}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Run Debug Tests (Check Console)
          </button>
        </div>

        {/* Test Scenarios */}
        <div className="space-y-8">
          {testScenarios.map((scenario, index) => (
            <div key={index} className="border rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-2">
                Test {index + 1}: {scenario.name}
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {scenario.description}
              </p>

              {/* Debug Info */}
              <div className="mb-4 p-3 bg-gray-100 rounded text-xs">
                <strong>Props:</strong>
                <pre>
                  {JSON.stringify(
                    {
                      productType: scenario.productType,
                      bundleVariantsCount: scenario.bundleVariants.length,
                      data: scenario.data || "N/A (BYOB mode)",
                    },
                    null,
                    2
                  )}
                </pre>
              </div>

              {/* Render Component */}
              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Rendered Component:</h4>
                <NutritionalFactsAccordion
                  productType={scenario.productType}
                  bundleVariants={scenario.bundleVariants}
                  data={scenario.data}
                  borderColor="#036A38"
                />
              </div>
            </div>
          ))}
        </div>

        {/* Data Structure Reference */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold mb-2">
            Expected Bundle Variant Structure:
          </h3>
          <pre className="text-xs overflow-auto">
            {`{
  id: number,
  systemId: string,
  title: string,
  primary_color: string,
  nutritional_facts: {
    show_component: boolean,
    nutritional_fact_details: [
      {
        id: number,
        title: string,
        value: string,
        unit: string,
      }
    ]
  }
}`}
          </pre>
        </div>

        {/* Debugging Checklist */}
        <div className="mt-8 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold mb-2">Debugging Checklist:</h3>
          <ul className="text-sm space-y-1">
            <li>
              ✅ Check if productType === "BYOB" is being passed correctly
            </li>
            <li>✅ Verify bundleVariants array is not empty</li>
            <li>
              ✅ Ensure each bundle variant has nutritional_facts property
            </li>
            <li>✅ Confirm nutritional_facts.show_component is true</li>
            <li>
              ✅ Validate nutritional_fact_details array exists and has data
            </li>
            <li>✅ Check console logs for component rendering decisions</li>
            <li>
              ✅ Verify props are being passed through component hierarchy
            </li>
          </ul>
        </div>

        {/* Accordion Layout Information */}
        <div className="mt-8 p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold mb-2">BYOB Accordion Layout (FAQ-Style):</h3>
          <ul className="text-sm space-y-1">
            <li>🎯 <strong>Single Accordion Container:</strong> All bundle variants in one accordion</li>
            <li>🎯 <strong>Seamless Borders:</strong> No gaps between accordion items</li>
            <li>🎯 <strong>Rounded Corners:</strong> Top item has rounded top, bottom item has rounded bottom</li>
            <li>🎯 <strong>Product Names as Headers:</strong> Each variant shows "{Product Name} - Nutritional Facts"</li>
            <li>🎯 <strong>Consistent Styling:</strong> Same fonts, colors, and dotted line separators as VARIANT</li>
            <li>🎯 <strong>FAQ Pattern:</strong> Matches the FAQ accordion component styling exactly</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default NutritionalFactsDebug;
