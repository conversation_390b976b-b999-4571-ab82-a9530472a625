/**
 * BundleVariantSelector Test Component
 * 
 * This component demonstrates and tests the enhanced BYOB functionality
 * with proper quantity controls and validation.
 */

"use client";

import React, { useState, useCallback } from "react";
import BundleVariantSelector from "./index";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import {
  validateBundleConfiguration,
  shouldDisablePlusButton,
  shouldDisableMinusButton,
  getMaxAllowedQuantityForVariant,
  formatBundleSummary,
} from "./BundleValidationUtils";

// Mock bundle variants data for testing
const mockBundleVariants: ProductDetailsType[] = [
  {
    id: 1,
    systemId: "hazelnut-cocoa",
    title: "Hazelnut Cocoa",
    primary_color: "#8B4513",
    metadata: { max_quantity: 15 },
  } as ProductDetailsType,
  {
    id: 2,
    systemId: "almond-millet-cocoa",
    title: "Almond Millet Cocoa",
    primary_color: "#DA70D6",
    metadata: { max_quantity: 10 },
  } as ProductDetailsType,
  {
    id: 3,
    systemId: "peanut-millet-cocoa",
    title: "Peanut Millet Cocoa",
    primary_color: "#DAA520",
    metadata: { max_quantity: 20 },
  } as ProductDetailsType,
  {
    id: 4,
    systemId: "double-cocoa",
    title: "Double Cocoa",
    primary_color: "#8B4B8B",
  } as ProductDetailsType,
  {
    id: 5,
    systemId: "cranberry",
    title: "Cranberry",
    primary_color: "#DC143C",
  } as ProductDetailsType,
  {
    id: 6,
    systemId: "coconut-cocoa",
    title: "Coconut Cocoa",
    primary_color: "#20B2AA",
  } as ProductDetailsType,
  {
    id: 7,
    systemId: "peanut-butter",
    title: "Peanut Butter",
    primary_color: "#FF8C00",
  } as ProductDetailsType,
  {
    id: 8,
    systemId: "orange-cocoa",
    title: "Orange Cocoa",
    primary_color: "#FF6347",
  } as ProductDetailsType,
];

const mockStrapiProduct: ProductDetailsType = {
  id: 1,
  systemId: "byob-bundle",
  title: "Build Your Own Bundle - 30 Pack",
  primary_color: "#036A38",
  bg_color: "#ffffff",
  metadata: { pack_size: 30 },
} as ProductDetailsType;

/**
 * Test Component for BundleVariantSelector
 */
const BundleVariantSelectorTest: React.FC = () => {
  const [selectedQuantities, setSelectedQuantities] = useState<Record<string, number>>({
    "hazelnut-cocoa": 5,
    "peanut-millet-cocoa": 5,
    "coconut-cocoa": 5,
    "orange-cocoa": 4,
  });

  const handleQuantityChange = useCallback((variantId: string, quantity: number) => {
    setSelectedQuantities(prev => ({
      ...prev,
      [variantId]: quantity
    }));
  }, []);

  // Test validation
  const validation = validateBundleConfiguration(selectedQuantities, 30);
  const bundleSummary = formatBundleSummary(selectedQuantities, mockBundleVariants);

  // Test button disable logic
  const testButtonStates = () => {
    console.log("🧪 Testing Button Disable Logic:");
    
    mockBundleVariants.forEach(variant => {
      const variantId = variant.systemId;
      const isPlusDisabled = shouldDisablePlusButton(
        variantId, 
        selectedQuantities, 
        mockBundleVariants, 
        30
      );
      const isMinusDisabled = shouldDisableMinusButton(variantId, selectedQuantities);
      const maxAllowed = getMaxAllowedQuantityForVariant(
        variantId, 
        selectedQuantities, 
        mockBundleVariants, 
        30
      );
      
      console.log(`${variant.title}:`, {
        currentQuantity: selectedQuantities[variantId] || 0,
        maxAllowed,
        plusDisabled: isPlusDisabled,
        minusDisabled: isMinusDisabled,
      });
    });
  };

  // Test edge cases
  const testEdgeCases = () => {
    console.log("🧪 Testing Edge Cases:");
    
    // Test: Bundle limit reached
    const fullBundle = {
      "hazelnut-cocoa": 15,
      "almond-millet-cocoa": 10,
      "peanut-millet-cocoa": 5,
    };
    const fullValidation = validateBundleConfiguration(fullBundle, 30);
    console.log("Full Bundle (30/30):", fullValidation);
    
    // Test: Exceeding bundle limit
    const overBundle = {
      "hazelnut-cocoa": 15,
      "almond-millet-cocoa": 10,
      "peanut-millet-cocoa": 10,
    };
    const overValidation = validateBundleConfiguration(overBundle, 30);
    console.log("Over Bundle (35/30):", overValidation);
    
    // Test: Individual variant max reached
    const maxVariantTest = shouldDisablePlusButton(
      "hazelnut-cocoa",
      { "hazelnut-cocoa": 15 },
      mockBundleVariants,
      30
    );
    console.log("Hazelnut at max (15/15):", { plusDisabled: maxVariantTest });
  };

  // Reset to test state
  const resetToTestState = () => {
    setSelectedQuantities({
      "hazelnut-cocoa": 5,
      "peanut-millet-cocoa": 5,
      "coconut-cocoa": 5,
      "orange-cocoa": 4,
    });
  };

  // Fill bundle to limit
  const fillToLimit = () => {
    setSelectedQuantities({
      "hazelnut-cocoa": 15,
      "almond-millet-cocoa": 10,
      "peanut-millet-cocoa": 5,
    });
  };

  // Clear all selections
  const clearAll = () => {
    setSelectedQuantities({});
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-4">BYOB Bundle Variant Selector Test</h1>
        
        {/* Test Controls */}
        <div className="mb-6 space-x-4">
          <button
            onClick={resetToTestState}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Reset to Test State (19/30)
          </button>
          <button
            onClick={fillToLimit}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Fill to Limit (30/30)
          </button>
          <button
            onClick={clearAll}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Clear All (0/30)
          </button>
          <button
            onClick={testButtonStates}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Test Button States
          </button>
          <button
            onClick={testEdgeCases}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Test Edge Cases
          </button>
        </div>

        {/* Bundle Status */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">Bundle Status:</h3>
          <p><strong>Current Total:</strong> {validation.currentTotal} / {validation.requiredTotal}</p>
          <p><strong>Valid:</strong> {validation.isValid ? "✅ Yes" : "❌ No"}</p>
          {validation.errorMessage && (
            <p><strong>Error:</strong> <span className="text-red-600">{validation.errorMessage}</span></p>
          )}
          <p><strong>Summary:</strong> {bundleSummary}</p>
        </div>

        {/* Bundle Variant Selector */}
        <BundleVariantSelector
          strapiProduct={mockStrapiProduct}
          bundleVariants={mockBundleVariants}
          productType="BYOB"
          onQuantityChange={handleQuantityChange}
          selectedQuantities={selectedQuantities}
        />

        {/* Debug Information */}
        <div className="mt-8 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">Debug Information:</h3>
          <pre className="text-xs overflow-auto">
            {JSON.stringify({
              selectedQuantities,
              validation,
              totalSelected: validation.currentTotal,
            }, null, 2)}
          </pre>
        </div>

        {/* Test Instructions */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold mb-2">Test Instructions:</h3>
          <ul className="text-sm space-y-1">
            <li>• Try adding items until you reach the 30-item limit</li>
            <li>• Notice how + buttons disable when limits are reached</li>
            <li>• Try removing items and see - buttons disable at 0</li>
            <li>• Test individual variant limits (Hazelnut: 15, Almond: 10, Peanut: 20)</li>
            <li>• Check that total bundle limit prevents exceeding 30 items</li>
            <li>• Verify cursor changes to "not-allowed" on disabled buttons</li>
            <li>• Test accessibility with keyboard navigation</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default BundleVariantSelectorTest;
