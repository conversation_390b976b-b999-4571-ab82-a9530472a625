"use client";

import React, { useCallback, useMemo } from "react";
import Image from "next/image";
import { BundleVariantSelectorProps, BundleQuantityValidation } from "../types";
import { getStrapiUrl } from "@/utils/strapiUrl";
import Minus from "@/assets/icons/Minus";
import Plus from "@/assets/icons/Plus";

/**
 * BundleVariantSelector Component
 *
 * Displays bundle variants with quantity selectors for BYOB products.
 * Only renders when productType === "BYOB".
 */
const BundleVariantSelector: React.FC<BundleVariantSelectorProps> = ({
  strapiProduct,
  bundleVariants,
  productType,
  onQuantityChange,
  selectedQuantities,
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";
  const backgroundColor = strapiProduct?.bg_color || "#ffffff";

  // Calculate total selected quantity
  const totalSelectedQuantity = useMemo(() => {
    return Object.values(selectedQuantities).reduce((sum, qty) => sum + qty, 0);
  }, [selectedQuantities]);

  // Get required pack size (you may need to adjust this based on your data structure)
  const requiredPackSize = useMemo(() => {
    // Try to get from Medusa bundle configuration or strapiProduct metadata
    // For now, using a default of 30 - adjust based on your actual data structure
    return 30; // This should come from your bundle configuration
  }, []);

  // Bundle quantity validation
  const validation: BundleQuantityValidation = useMemo(() => {
    const isValid = totalSelectedQuantity === requiredPackSize;
    const errorMessage = !isValid
      ? `Please add exactly ${requiredPackSize} items to complete your bundle`
      : undefined;

    return {
      isValid,
      currentTotal: totalSelectedQuantity,
      requiredTotal: requiredPackSize,
      errorMessage,
    };
  }, [totalSelectedQuantity, requiredPackSize]);

  const handleQuantityChange = useCallback(
    (variantId: string, newQuantity: number) => {
      // Ensure quantity is not negative
      const clampedQuantity = Math.max(0, newQuantity);
      onQuantityChange(variantId, clampedQuantity);
    },
    [onQuantityChange]
  );

  const incrementQuantity = useCallback(
    (variantId: string) => {
      const currentQuantity = selectedQuantities[variantId] || 0;
      handleQuantityChange(variantId, currentQuantity + 1);
    },
    [selectedQuantities, handleQuantityChange]
  );

  const decrementQuantity = useCallback(
    (variantId: string) => {
      const currentQuantity = selectedQuantities[variantId] || 0;
      handleQuantityChange(variantId, currentQuantity - 1);
    },
    [selectedQuantities, handleQuantityChange]
  );

  // Type guard: Only render for BYOB products
  if (productType !== "BYOB") {
    return null;
  }

  return (
    <div className="mb-6">
      {/* Bundle Header */}
      <div className="mb-4">
        <h3
          className="text-lg font-semibold font-narrow mb-2"
          style={{ color: primaryColor }}
        >
          Build Your Bundle
        </h3>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Selected: {totalSelectedQuantity} / {requiredPackSize} items
          </span>
          {!validation.isValid && (
            <span
              className="text-sm font-medium"
              style={{ color: primaryColor }}
            >
              {validation.errorMessage}
            </span>
          )}
        </div>
      </div>

      {/* Bundle Variants Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-4">
        {bundleVariants.map((variant) => {
          const variantId = variant.systemId || "";
          const currentQuantity = selectedQuantities[variantId] || 0;
          const variantImage = (variant as any).images?.[0]?.web?.url;

          return (
            <div
              key={variantId}
              className="border rounded-lg p-4 transition-all duration-200 hover:shadow-md"
              style={{
                borderColor: currentQuantity > 0 ? primaryColor : "#e5e7eb",
                backgroundColor:
                  currentQuantity > 0 ? `${backgroundColor}20` : "white",
              }}
            >
              {/* Variant Image and Info */}
              <div className="flex items-start gap-3 mb-3">
                {variantImage && (
                  <div className="relative w-16 h-16 flex-shrink-0">
                    <Image
                      src={getStrapiUrl(variantImage)}
                      alt={variant.title || "Bundle variant"}
                      fill
                      className="object-contain rounded"
                    />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm text-gray-900 line-clamp-2">
                    {variant.title}
                  </h4>
                  {variant.short_description && (
                    <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                      {variant.short_description}
                    </p>
                  )}
                </div>
              </div>

              {/* Quantity Selector */}
              <div className="flex items-center justify-center">
                <div
                  className="h-10 rounded-sm overflow-hidden flex items-center justify-between border bg-white min-w-[120px]"
                  style={{ borderColor: primaryColor }}
                >
                  <button
                    className="h-10 w-10 flex items-center justify-center cursor-pointer transition-colors duration-200 hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-1"
                    style={{
                      backgroundColor: primaryColor,
                    }}
                    onClick={() => decrementQuantity(variantId)}
                    disabled={currentQuantity <= 0}
                    aria-label={`Decrease quantity for ${variant.title}`}
                  >
                    <Minus />
                  </button>
                  <div
                    className="flex-1 text-center text-sm font-medium leading-5 font-obviously px-2"
                    style={{ color: primaryColor }}
                  >
                    {currentQuantity}
                  </div>
                  <button
                    className="h-10 w-10 flex items-center justify-center cursor-pointer transition-colors duration-200 hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-1"
                    style={{
                      backgroundColor: primaryColor,
                    }}
                    onClick={() => incrementQuantity(variantId)}
                    aria-label={`Increase quantity for ${variant.title}`}
                  >
                    <Plus />
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Validation Summary */}
      {!validation.isValid && totalSelectedQuantity > 0 && (
        <div
          className="mt-4 p-3 rounded-md border"
          style={{
            borderColor: primaryColor,
            backgroundColor: `${backgroundColor}10`,
          }}
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">
              Bundle Progress: {validation.currentTotal} /{" "}
              {validation.requiredTotal}
            </span>
            <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full transition-all duration-300"
                style={{
                  backgroundColor: primaryColor,
                  width: `${Math.min(
                    100,
                    (validation.currentTotal / validation.requiredTotal) * 100
                  )}%`,
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BundleVariantSelector;
