import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { ExtendedMedusaProductWithStrapiProduct } from "@/types/Medusa/Product";
import React from "react";

const BundleVariantSelector = ({
  strapiProduct,
  medusaProduct,
}: {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}) => {
  return <div>
    {/* here i want to show a counter with + and - buttons in primary and bg color based on bundle varints */}
  </div>;
};

export default BundleVariantSelector;
