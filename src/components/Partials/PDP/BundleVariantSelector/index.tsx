"use client";

import React, { useCallback, useMemo } from "react";
import { BundleVariantSelectorProps, BundleQuantityValidation } from "../types";
import Minus from "@/assets/icons/Minus";
import Plus from "@/assets/icons/Plus";
import {
  calculateTotalQuantity,
  getRequiredPackSize,
  validateBundleConfiguration,
  getVariantMaxQuantity,
  calculateBundleProgress,
  getBundleValidationStatus,
} from "./BundleValidationUtils";

/**
 * Get variant color by index for consistent color scheme
 */
const getVariantColorByIndex = (index: number): string => {
  const colors = [
    "#8B4513", // Brown (Hazelnut Cocoa)
    "#DA70D6", // Orchid (Almond Millet Cocoa)
    "#DAA520", // Goldenrod (Peanut Millet Cocoa)
    "#8B4B8B", // Dark Magenta (Double Cocoa)
    "#DC143C", // Crimson (Cranberry)
    "#20B2AA", // Light Sea Green (Coconut Cocoa)
    "#FF8C00", // Dark Orange (Peanut Butter)
    "#FF6347", // Tomato (Orange Cocoa)
  ];

  return colors[index % colors.length] || "#036A38";
};

/**
 * BundleVariantSelector Component
 *
 * Displays bundle variants with quantity selectors for BYOB products.
 * Only renders when productType === "BYOB".
 */
const BundleVariantSelector: React.FC<BundleVariantSelectorProps> = ({
  strapiProduct,
  bundleVariants,
  productType,
  onQuantityChange,
  selectedQuantities,
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";
  const backgroundColor = strapiProduct?.bg_color || "#ffffff";

  // Calculate total selected quantity using utility function
  const totalSelectedQuantity = useMemo(() => {
    return calculateTotalQuantity(selectedQuantities);
  }, [selectedQuantities]);

  // Get required pack size using utility function
  const requiredPackSize = useMemo(() => {
    return getRequiredPackSize(strapiProduct);
  }, [strapiProduct]);

  // Bundle quantity validation using utility function
  const validation: BundleQuantityValidation = useMemo(() => {
    return validateBundleConfiguration(selectedQuantities, requiredPackSize);
  }, [selectedQuantities, requiredPackSize]);

  // Get validation status for UI styling
  const validationStatus = useMemo(() => {
    return getBundleValidationStatus(validation);
  }, [validation]);

  // Calculate progress percentage
  const progressPercentage = useMemo(() => {
    return calculateBundleProgress(
      validation.currentTotal,
      validation.requiredTotal
    );
  }, [validation]);

  // Check if plus button should be disabled for a specific variant
  const isPlusButtonDisabled = useCallback(
    (variantId: string) => {
      const currentQuantity = selectedQuantities[variantId] || 0;
      const variant = bundleVariants.find((v) => v.systemId === variantId);
      const maxQuantity = variant ? getVariantMaxQuantity(variant) : undefined;

      // Disable if total bundle limit reached
      if (totalSelectedQuantity >= requiredPackSize) {
        return true;
      }

      // Disable if individual variant max quantity reached
      if (maxQuantity !== undefined && currentQuantity >= maxQuantity) {
        return true;
      }

      return false;
    },
    [
      selectedQuantities,
      bundleVariants,
      totalSelectedQuantity,
      requiredPackSize,
    ]
  );

  // Check if minus button should be disabled for a specific variant
  const isMinusButtonDisabled = useCallback(
    (variantId: string) => {
      const currentQuantity = selectedQuantities[variantId] || 0;
      return currentQuantity <= 0;
    },
    [selectedQuantities]
  );

  const handleQuantityChange = useCallback(
    (variantId: string, newQuantity: number) => {
      // Get the variant to check for max quantity
      const variant = bundleVariants.find((v) => v.systemId === variantId);
      const maxQuantity = variant ? getVariantMaxQuantity(variant) : undefined;

      // Ensure quantity is not negative and respects max quantity
      let clampedQuantity = Math.max(0, newQuantity);
      if (maxQuantity !== undefined) {
        clampedQuantity = Math.min(clampedQuantity, maxQuantity);
      }

      // Ensure total bundle limit is not exceeded
      const otherVariantsTotal = Object.entries(selectedQuantities)
        .filter(([id]) => id !== variantId)
        .reduce((sum, [, qty]) => sum + qty, 0);

      const maxAllowedForThisVariant = requiredPackSize - otherVariantsTotal;
      clampedQuantity = Math.min(clampedQuantity, maxAllowedForThisVariant);

      onQuantityChange(variantId, clampedQuantity);
    },
    [onQuantityChange, bundleVariants, selectedQuantities, requiredPackSize]
  );

  const incrementQuantity = useCallback(
    (variantId: string) => {
      if (isPlusButtonDisabled(variantId)) return;

      const currentQuantity = selectedQuantities[variantId] || 0;
      handleQuantityChange(variantId, currentQuantity + 1);
    },
    [selectedQuantities, handleQuantityChange, isPlusButtonDisabled]
  );

  const decrementQuantity = useCallback(
    (variantId: string) => {
      if (isMinusButtonDisabled(variantId)) return;

      const currentQuantity = selectedQuantities[variantId] || 0;
      handleQuantityChange(variantId, currentQuantity - 1);
    },
    [selectedQuantities, handleQuantityChange, isMinusButtonDisabled]
  );

  // Type guard: Only render for BYOB products
  if (productType !== "BYOB") {
    return null;
  }

  return (
    <div className="mb-6">
      {/* Bundle Header */}
      <div className="mb-4">
        <h3
          className="text-lg font-semibold font-narrow mb-2"
          style={{ color: primaryColor }}
        >
          Build Your Bundle
        </h3>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Selected: {totalSelectedQuantity} / {requiredPackSize} items
          </span>
          {!validation.isValid && (
            <span
              className={`text-sm font-medium ${
                validationStatus === "excess" ? "text-red-600" : ""
              }`}
              style={{
                color: validationStatus === "excess" ? "#dc2626" : primaryColor,
              }}
            >
              {validation.errorMessage}
            </span>
          )}
          {validation.isValid && (
            <span className="text-sm font-medium text-green-600">
              ✓ Bundle Complete
            </span>
          )}
        </div>
      </div>

      {/* Bundle Variants List */}
      <div className="space-y-0 border border-gray-200 rounded-lg overflow-hidden">
        {bundleVariants.map((variant, index) => {
          const variantId = variant.systemId || "";
          const currentQuantity = selectedQuantities[variantId] || 0;
          const isPlusDisabled = isPlusButtonDisabled(variantId);
          const isMinusDisabled = isMinusButtonDisabled(variantId);

          // Get variant color from metadata or use a default color scheme
          const variantColor =
            (variant as any).primary_color || getVariantColorByIndex(index);

          return (
            <div
              key={variantId}
              className={`flex items-center justify-between p-4 ${
                index !== bundleVariants.length - 1
                  ? "border-b border-gray-200"
                  : ""
              }`}
              style={{
                backgroundColor: `${variantColor}15`, // Light tint of variant color
              }}
            >
              {/* Variant Title */}
              <div className="flex-1">
                <h4
                  className="font-bold text-lg font-narrow"
                  style={{ color: variantColor }}
                >
                  {variant.title}
                </h4>
              </div>

              {/* Quantity Selector */}
              <div className="flex items-center">
                <div
                  className="h-12 rounded-lg overflow-hidden flex items-center bg-white border-2 min-w-[140px]"
                  style={{ borderColor: variantColor }}
                >
                  {/* Minus Button */}
                  <button
                    className={`h-12 w-12 flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${
                      isMinusDisabled
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:opacity-80 cursor-pointer"
                    }`}
                    style={{
                      backgroundColor: variantColor,
                      cursor: isMinusDisabled ? "not-allowed" : "pointer",
                    }}
                    onClick={() => decrementQuantity(variantId)}
                    disabled={isMinusDisabled}
                    aria-label={`Decrease quantity for ${variant.title}`}
                  >
                    <Minus className="text-white" />
                  </button>

                  {/* Quantity Display */}
                  <div className="flex-1 text-center text-lg font-bold font-obviously px-3 bg-white">
                    {currentQuantity}
                  </div>

                  {/* Plus Button */}
                  <button
                    className={`h-12 w-12 flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${
                      isPlusDisabled
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:opacity-80 cursor-pointer"
                    }`}
                    style={{
                      backgroundColor: variantColor,
                      cursor: isPlusDisabled ? "not-allowed" : "pointer",
                    }}
                    onClick={() => incrementQuantity(variantId)}
                    disabled={isPlusDisabled}
                    aria-label={`Increase quantity for ${variant.title}`}
                  >
                    <Plus className="text-white" />
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Validation Summary */}
      {!validation.isValid && totalSelectedQuantity > 0 && (
        <div
          className="mt-4 p-3 rounded-md border"
          style={{
            borderColor: primaryColor,
            backgroundColor: `${backgroundColor}10`,
          }}
        >
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">
              Bundle Progress: {validation.currentTotal} /{" "}
              {validation.requiredTotal}
            </span>
            <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full transition-all duration-300"
                style={{
                  backgroundColor: primaryColor,
                  width: `${progressPercentage}%`,
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BundleVariantSelector;
