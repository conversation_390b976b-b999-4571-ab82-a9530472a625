import React from "react";
import VariantSelector from "../../VariantSelector";
import QuantitySelector from "../../QuantitySelector";
import AddToCartButton from "../../AddToCartButton";
import BestPriceCoupon from "../../BestPriceCoupon";
import DeliveryInfo from "../../DeliveryInfoCard";
import { ProductActionsProps } from "../../types";
import BundleVariantSelector from "../../BundleVariantSelector";

/**
 * ProductActions Component
 *
 * Contains all product interaction elements:
 * - Variant selection
 * - Quantity selection
 * - Add to cart button
 * - Best price coupon
 * - Delivery information
 */
export const ProductActions: React.FC<ProductActionsProps> = ({
  strapiProduct,
  medusaProduct,
  onAddToCart,
  onCouponClick,
  productType,
}) => {
  const handleCouponClick = () => {
    if (onCouponClick) {
      onCouponClick();
    } else {
      console.log("Coupon clicked");
    }
  };

  return (
    <>
      <div className="flex flex-col">
        {/* Variant Selector When productType is VARIANT otherwise hide */}
        <VariantSelector
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
        />

        {/* Bundle Variant Selector When productType is BYOB ot
        hide */}
        <BundleVariantSelector
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
        />

        {/* Quantity Selector */}
        <QuantitySelector
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
        />

        {/* Add to Cart Button */}
        <AddToCartButton
          strapiProduct={strapiProduct}
          medusaProduct={medusaProduct}
          onAddToCart={onAddToCart}
        />
      </div>

      {/* Best Price Coupon */}
      <BestPriceCoupon
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
        onCouponClick={handleCouponClick}
      />

      {/* Delivery Information */}
      <DeliveryInfo
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
      />
    </>
  );
};
