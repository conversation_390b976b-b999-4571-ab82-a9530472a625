import React from "react";
import ProductShortDescription from "../../ProductShortDescription";
import WhatsInsideSection from "../../WhatsInsideSection";
import ProductAdditionalDescription from "../../ProductAdditionalDescription";
import { ProductDescriptionProps } from "../../types";

/**
 * ProductDescription Component
 *
 * Contains all product description sections:
 * - Short description
 * - What's inside section
 * - Additional description
 */
export const ProductDescription: React.FC<ProductDescriptionProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  return (
    <>
      {/* Short Description */}
      <ProductShortDescription description={strapiProduct?.short_description} />

      {/* What's Inside Section */}
      <WhatsInsideSection
        data={strapiProduct?.whats_inside}
        primaryColor={strapiProduct?.primary_color}
      />

      {/* Additional Description */}
      <ProductAdditionalDescription
        data={strapiProduct?.additional_description}
      />
    </>
  );
};
