"use client";

import React from "react";
import { cn } from "@/libs/utils";
import { ProductContentProps } from "../../types";
import { ProductMedia } from "./ProductMedia";
import { ProductDescription } from "./ProductDescription";
import { ProductDetails } from "./ProductDetails";

/**
 * ProductContent Component
 *
 * Main product content section containing:
 * - Product media (carousel, images)
 * - Product descriptions and details
 * - Product reviews and additional information
 */
export const ProductContent: React.FC<ProductContentProps> = ({
  className,
  strapiProduct,
  medusaProduct,
  productType = "VARIANT",
  bundleVariants = [],
}) => {
  console.log("🔍 ProductContent Component Debug:", {
    productType,
    bundleVariantsLength: bundleVariants?.length || 0,
    strapiProductTitle: strapiProduct?.title,
  });
  return (
    <div className={cn("col-span-12 lg:col-span-7", className)}>
      {/* Product Media */}
      <ProductMedia
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
      />

      {/* Product Description */}
      <ProductDescription
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
      />

      {/* Product Details */}
      <ProductDetails
        strapiProduct={strapiProduct}
        medusaProduct={medusaProduct}
        productType={productType}
        bundleVariants={bundleVariants}
      />
    </div>
  );
};
