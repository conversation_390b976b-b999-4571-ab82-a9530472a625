import React from "react";
import NutritionalFactsAccordion from "../../NutritionalFacts";
import ProductDetailsAccordion from "../../ProductDetailsAccordion";
import { ProductDetailsProps } from "../../types";

/**
 * ProductDetails Component
 *
 * Contains product detail accordions:
 * - Nutritional facts
 * - Product details
 */
export const ProductDetails: React.FC<ProductDetailsProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  return (
    <>
      {/* Nutritional Facts */}
      <div className="mt-4">
        <NutritionalFactsAccordion
          data={strapiProduct?.nutritional_facts}
          borderColor={primaryColor}
          productType="VARIANT"
          bundleVariants={[]}
        />
      </div>

      {/* Product Details */}
      <ProductDetailsAccordion
        data={strapiProduct?.product_detail_extra}
        borderColor={primaryColor}
      />
    </>
  );
};
