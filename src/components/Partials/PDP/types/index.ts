import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import {
  ExtendedMedusaProductWithStrapiProduct,
  ExtendedVariant,
} from "@/types/Medusa/Product";

// ============================================================================
// CORE PRODUCT TYPES
// ============================================================================

export interface ProductData {
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;
}

export interface ProductUIState {
  activeVariant: ExtendedVariant | null;
  quantity: number;
  currentSlide: number;
  isModalOpen: boolean;
  selectedImageIndex: number;
}

export interface ProductTheme {
  primaryColor: string;
  backgroundColor: string;
}

export interface ProductLoadingState {
  isLoading: boolean;
  isError: boolean;
  error: string | null;
}

// ============================================================================
// CONTEXT STATE INTERFACES
// ============================================================================

export interface ProductContextState
  extends ProductData,
    ProductUIState,
    ProductTheme,
    ProductLoadingState {
  // Computed values
  combinedImages: string[];
  currentPrice: number;
  originalPrice: number;
  loyaltyPoints: number;
  discountPercentage: number;
  breadcrumbItems: Array<{ label: string; href?: string }>;
}

export interface ProductContextActions {
  // Variant & Quantity Actions
  setActiveVariant: (variant: ExtendedVariant) => void;
  setQuantity: (quantity: number) => void;
  incrementQuantity: () => void;
  decrementQuantity: () => void;

  // UI Actions
  setCurrentSlide: (slide: number) => void;
  openModal: (imageIndex?: number) => void;
  closeModal: () => void;

  // Cart Actions
  addToCart: (data?: Partial<AddToCartData>) => Promise<void>;

  // Coupon Actions
  applyCoupon: () => void;
}

export interface ProductContextValue
  extends ProductContextState,
    ProductContextActions {}

// ============================================================================
// CART & PRICING TYPES
// ============================================================================

export interface AddToCartData {
  product: ExtendedMedusaProductWithStrapiProduct;
  variant: ExtendedVariant;
  quantity: number;
  totalPrice: number;
}

export interface PricingData {
  currentPrice: number;
  originalPrice: number;
  discountPercentage: number;
  loyaltyPoints: number;
  cashbackPercentage: number;
}

// ============================================================================
// COMPONENT PROP INTERFACES
// ============================================================================

export interface ProductProviderProps {
  children: React.ReactNode;
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  onAddToCart?: (data: AddToCartData) => void;
  onCouponClick?: () => void;
}

export interface ProductDetailsPageProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  onAddToCart?: (data: AddToCartData) => void;
  onCouponClick?: () => void;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: ProductDetailsType[];
}

export interface ProductLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export interface ProductInfoProps {
  className?: string;
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  onAddToCart?: (data: AddToCartData) => void;
  onCouponClick?: () => void;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: ProductDetailsType[];
}

export interface ProductContentProps {
  className?: string;
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: ProductDetailsType[];
}

export interface ProductMediaProps {
  className?: string;
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}

export interface ProductActionsProps {
  className?: string;
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  onAddToCart?: (data: AddToCartData) => void;
  onCouponClick?: () => void;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: ProductDetailsType[];
}

export interface ProductHeaderProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}

export interface ProductPricingProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}

export interface ProductMobileCarouselProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}

export interface ProductDescriptionProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}

export interface ProductDetailsProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: ProductDetailsType[];
}

// Additional component prop interfaces
export interface VariantSelectorProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}

export interface QuantitySelectorProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  label?: string;
}

export interface AddToCartButtonProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  onAddToCart?: (data: AddToCartData) => void;
}

export interface BestPriceCouponProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
  onCouponClick?: () => void;
}

export interface DeliveryInfoProps {
  strapiProduct?: ProductDetailsType;
  medusaProduct?: ExtendedMedusaProductWithStrapiProduct;
}

// ============================================================================
// BYOB COMPONENT TYPES
// ============================================================================

export interface BundleVariantSelectorProps {
  strapiProduct: ProductDetailsType; // Main BYOB product for styling
  bundleVariants: ProductDetailsType[]; // Array of selectable bundle variants
  productType: "BYOB" | "VARIANT"; // For conditional rendering
  onQuantityChange: (variantId: string, quantity: number) => void;
  selectedQuantities: Record<string, number>; // Current selections
}

export interface BundleQuantityValidation {
  isValid: boolean;
  currentTotal: number;
  requiredTotal: number;
  errorMessage?: string;
}

export interface WhatsInsideSectionProps {
  data?: any; // WhatsInsideType or array for BYOB
  primaryColor?: string;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: ProductDetailsType[];
}

export interface NutritionalFactsAccordionProps {
  data?: any; // NutritionalFactsType or array for BYOB
  borderColor?: string;
  iconColor?: string;
  className?: string;
  productType?: "BYOB" | "VARIANT";
  bundleVariants?: ProductDetailsType[];
}

// ============================================================================
// HOOK RETURN TYPES
// ============================================================================

export interface UseProductDataReturn {
  strapiProduct: ProductDetailsType | null;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct | null;
  isLoading: boolean;
  isError: boolean;
  error: string | null;
}

export interface UseProductImagesReturn {
  images: string[];
  currentImage: string;
  hasImages: boolean;
}

export interface UseProductPricingReturn extends PricingData {
  formattedCurrentPrice: string;
  formattedOriginalPrice: string;
  hasDiscount: boolean;
}

export interface UseProductVariantsReturn {
  variants: ExtendedVariant[];
  activeVariant: ExtendedVariant | null;
  setActiveVariant: (variant: ExtendedVariant) => void;
  hasVariants: boolean;
}

export interface UseProductCartReturn {
  quantity: number;
  setQuantity: (quantity: number) => void;
  incrementQuantity: () => void;
  decrementQuantity: () => void;
  addToCart: () => Promise<void>;
  isAddingToCart: boolean;
  canAddToCart: boolean;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type ProductContextProviderProps = ProductProviderProps;

export type ProductComponent<T = Record<string, unknown>> = React.FC<T>;

export type ProductHook<T = any> = () => T;

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface ProductError {
  code: string;
  message: string;
  details?: any;
}

export type ProductErrorType =
  | "PRODUCT_NOT_FOUND"
  | "VARIANT_NOT_AVAILABLE"
  | "CART_ERROR"
  | "PRICING_ERROR"
  | "IMAGE_LOAD_ERROR"
  | "NETWORK_ERROR"
  | "UNKNOWN_ERROR";
