"use client";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import React from "react";
import { ProductDetailsPage } from "../ProductDetailsPage";
import { ExtendedMedusaProductWithStrapiProduct } from "@/types/Medusa/Product";

/**
 * ProductView Component
 *
 * Wrapper component that renders the ProductDetailsPage with server-side data
 * and proper error handling. No longer uses Redux state management.
 */

const ProductView = ({
  productDataOk,
  medusaProductData,
}: {
  medusaProductData?: ExtendedMedusaProductWithStrapiProduct;
  productDataOk?: ProductDetailsType;
} = {}) => {
  console.log("productDataOk", productDataOk);
  console.log("medusaProductData", medusaProductData);

  const handleAddToCart = (cartData: any) => {
    console.log("Adding to cart:", cartData);
    // Handle add to cart logic here
  };

  const handleCouponClick = () => {
    console.log("Coupon clicked");
    // Handle coupon click logic here
  };

  // Show loading state if no data is available
  if (!medusaProductData && !productDataOk) {
    return (
      <div className="max-w-[1164px] px-6 mx-auto">
        <div className="pt-10 pb-12">
          <div className="flex items-center justify-center min-h-[400px]">
            <p className="text-lg text-gray-600">Loading product...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ProductDetailsPage
      strapiProduct={productDataOk}
      medusaProduct={medusaProductData}
      onAddToCart={handleAddToCart}
      onCouponClick={handleCouponClick}
    />
  );
};

export default ProductView;
