import { sdk } from "@/libs/medusaClient";
import { getStrapiProductDetails } from "@/libs/strapiApis";
import { FetchResult } from "@/types/Common";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";

interface FetchProductDataParams {
  productHandle?: string;
}

export async function fetchProductData({
  productHandle,
}: FetchProductDataParams): Promise<FetchResult> {
  try {
    // Here first i neeed to fetch the product from medusa by handle
    const medusaProductByHandle = await sdk.store.product.list({
      handle: productHandle,
      fields:
        "+variants.calculated_price.*,+variants.extended_product_variants.*,cms_product.*,+variants.prices.*,+variants.variant_image.*",
      region_id: "reg_01JWZRBZKN5YNMT05GWVNQCVBR",
    });

    console.log("medusaProductByHandle: ", medusaProductByHandle);

    const checkProductType = medusaProductByHandle.products[0].type?.value;
    console.log("checkProductType: ", checkProductType);

    enum ProductType {
      VARIANT = "VARIANT",
      BYOB = "BYOB",
    }

    if (checkProductType === ProductType.BYOB) {
      const variantId =
        medusaProductByHandle?.products &&
        medusaProductByHandle.products[0] &&
        medusaProductByHandle.products[0].variants &&
        medusaProductByHandle.products[0].variants[0]
          ? medusaProductByHandle.products[0].variants[0].id
          : undefined;

      console.log("variantId: ", variantId);

      // I AM BYOB PRODUCT NEED TO FETCH BYOB PRODUCT FROM MEDUSA
      const byobProduct = await sdk.client.fetch(
        `/store/bundles/${variantId}`,
        {
          method: "GET",
          cache: "force-cache",
        }
      );

      console.log("byobProduct: ", byobProduct.data.bundle.variants);

      // Extract ids to fetch the data from the strapi
      const strapiIds = byobProduct.data.bundle.variants.map(
        (variant: any) => variant.product_id
      );

      console.log("strapiIds: ", strapiIds);
    }

    const strapiProduct = await getStrapiProductDetails({ productHandle });

    if (!strapiProduct) {
      return { success: false, error: "Product not found in Strapi" };
    }

    const typedStrapiProduct = strapiProduct as unknown as ProductDetailsType;

    // Get the Medusa product ID from Strapi product
    const medusaProductId = typedStrapiProduct.systemId as string;

    if (!medusaProductId) {
      return {
        success: false,
        error: "Product ID not found for Medusa",
      };
    }

    // Fetch the Medusa product using the ID from Strapi
    // const medusaProduct = await sdk.store.product.list({
    //   handle: "mango-milkshake-24g-protein-powder",
    //   fields:
    //     "+variants.calculated_price.*,+variants.extended_product_variants.*,cms_product.*,+variants.prices.*,+variants.variant_image.*",
    //   region_id: "reg_01JWZRBZKN5YNMT05GWVNQCVBR",
    // });

    // if (!medusaProduct || !medusaProduct.products?.length) {
    //   return {
    //     success: false,
    //     error: "Product not found in Medusa",
    //   };
    // }

    return {
      success: true,
      data: {
        strapiProduct: typedStrapiProduct,
        medusaProduct: medusaProductByHandle.products[0],
      },
    };
  } catch (error) {
    console.error("Error in fetchProductData:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? `Failed to fetch product data: ${error.message}`
          : "An unknown error occurred while fetching product data",
    };
  }
}
